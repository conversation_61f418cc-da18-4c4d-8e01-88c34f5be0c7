import { NextResponse } from "next/server";

// <PERSON><PERSON> auth sementara untuk development
export async function GET() {
  try {
    // Gunakan dummy data untuk pengembangan tanpa pemeriksaan auth
    const dummyData = {
      totalRevenue: 127500000,
      totalRentals: 35,
      totalProducts: 12,
      occupancyRate: 75,
      revenueGrowth: 12.5,
      rentalGrowth: 8.2,
      productGrowth: 2.7,
      occupancyGrowth: -3.1,
      recentRentals: [
        {
          id: "1",
          productName: "Genset 60kVA Industrial",
          customerName: "PT ABC Konstruksi",
          amount: 3500000,
          status: "COMPLETED",
          date: new Date(2023, 9, 22),
        },
        {
          id: "2",
          productName: "Genset 10kVA Silent Type",
          customerName: "Ahmad Fadli",
          amount: 1500000,
          status: "ACTIVE",
          date: new Date(2023, 9, 20),
        },
        {
          id: "3",
          productName: "Genset 25kVA Silent Type",
          customerName: "CV Berkarya Jaya",
          amount: 2250000,
          status: "PENDING",
          date: new Date(2023, 9, 18),
        },
        {
          id: "4",
          productName: "Genset 100kVA Heavy Duty",
          customerName: "PT XYZ Industri",
          amount: 5000000,
          status: "ACTIVE",
          date: new Date(2023, 9, 15),
        },
        {
          id: "5",
          productName: "Genset 5kVA Portable",
          customerName: "Budi Santoso",
          amount: 850000,
          status: "COMPLETED",
          date: new Date(2023, 9, 12),
        },
      ],
      monthlyRevenue: [
        { month: "Jan", revenue: 9500000 },
        { month: "Feb", revenue: 8700000 },
        { month: "Mar", revenue: 12300000 },
        { month: "Apr", revenue: 10500000 },
        { month: "Mei", revenue: 9800000 },
        { month: "Jun", revenue: 11200000 },
        { month: "Jul", revenue: 13500000 },
        { month: "Agu", revenue: 14800000 },
        { month: "Sep", revenue: 16200000 },
        { month: "Okt", revenue: 15700000 },
        { month: "Nov", revenue: 0 },
        { month: "Des", revenue: 0 },
      ],
      monthlyRentals: [
        { month: "Jan", count: 3 },
        { month: "Feb", count: 2 },
        { month: "Mar", count: 4 },
        { month: "Apr", count: 3 },
        { month: "Mei", count: 2 },
        { month: "Jun", count: 3 },
        { month: "Jul", count: 5 },
        { month: "Agu", count: 4 },
        { month: "Sep", count: 6 },
        { month: "Okt", count: 3 },
        { month: "Nov", count: 0 },
        { month: "Des", count: 0 },
      ],
      topProducts: [
        { id: "1", name: "Genset 100 KVA", capacity: 100, rentalCount: 28 },
        { id: "2", name: "Genset 50 KVA", capacity: 50, rentalCount: 22 },
        { id: "3", name: "Genset 25 KVA", capacity: 25, rentalCount: 18 },
        { id: "4", name: "Genset 10 KVA", capacity: 10, rentalCount: 15 }
      ]
    };

    // Log untuk debugging
    console.log("Dashboard stats API called successfully - development mode");
    
    // Mengembalikan data dummy
    return NextResponse.json(dummyData);
    
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return new NextResponse(
      JSON.stringify({ message: "Internal Server Error" }),
      { status: 500 }
    );
  }
} 
