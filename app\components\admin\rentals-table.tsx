"use client";

import React, { useMemo } from "react";
import { formatCurrency } from "@/lib/utils/format";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";


import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/app/components/ui/dropdown-menu";
import { MoreVertical, Eye } from "lucide-react";
import Link from "next/link";

type RentalWithRelations = {
  id: string;
  startDate: Date;
  endDate: Date;
  operationalStart: Date | null;
  operationalEnd: Date | null;
  address: string | null;
  amount: number;
  status: string;
  createdAt: Date;
  arrivalTime?: string | null;
  duration?: string | null;
  notes?: string | null;
  user: {
    name: string;
    email: string;
    phone: string;
  };
  product: {
    name: string;
    capacity: number;
  };
  payment: {
    id: string;
    status: string;
  } | null;
};

interface RentalsTableProps {
  rentals: RentalWithRelations[];
  searchQuery?: string;
}

// Fungsi untuk mendapatkan tampilan status rental
function getRentalStatusBadge(status: string) {
  switch (status.toUpperCase()) {
    case "CONFIRMED":
      return { label: "Dikonfirmasi", color: "bg-blue-100 text-blue-800" };
    case "ONGOING":
      return { label: "Berlangsung", color: "bg-green-100 text-green-800" };
    case "COMPLETED":
      return { label: "Selesai", color: "bg-gray-100 text-gray-800" };
    case "CANCELLED":
      return { label: "Dibatalkan", color: "bg-red-100 text-red-800" };
    default:
      return { label: "Menunggu", color: "bg-yellow-100 text-yellow-800" };
  }
}

export function RentalsTable({ rentals, searchQuery = "" }: RentalsTableProps) {
  // Filter rentals based on search
  const filteredRentals = useMemo(() => {
    if (!searchQuery) return rentals;

    return rentals.filter(rental =>
      rental.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.user.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.product.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [rentals, searchQuery]);

  // Pagination - Read from URL
  const [currentPage, setCurrentPage] = React.useState(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return parseInt(urlParams.get('page') || '1', 10);
    }
    return 1;
  });
  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredRentals.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  // Reset page when search changes
  React.useEffect(() => {
    if (searchQuery) {
      setCurrentPage(1);
    }
  }, [searchQuery]);

  const paginatedRentals = useMemo(() => {
    return filteredRentals.slice(startIndex, endIndex);
  }, [filteredRentals, startIndex, endIndex]);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    }).format(new Date(date));
  };

  const formatDateTime = (date: Date | null) => {
    if (!date) return '-';
    return new Intl.DateTimeFormat('id-ID', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const calculateDuration = (start: Date, end: Date) => {
    const diffTime = Math.abs(new Date(end).getTime() - new Date(start).getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} hari`;
  };

  return (
    <div className="space-y-6">
      <Card className="border-border shadow-sm">
        <CardHeader className="border-b border-border bg-muted/50">
          <CardTitle className="text-foreground">Daftar Rental</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto scrollbar-hide">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border bg-muted/30 hover:bg-muted/50">
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">No</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Produk</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Pelanggan</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Tanggal Mulai</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Tanggal Selesai</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Durasi</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium">Status</TableHead>
                  <TableHead className="h-12 px-6 text-muted-foreground font-medium w-[60px]">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedRentals.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-12 text-muted-foreground">
                      {searchQuery ? `Tidak ada hasil untuk "${searchQuery}"` : "Tidak ada data rental"}
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedRentals.map((rental, index) => {
                    const statusBadge = getRentalStatusBadge(rental.status);
                    const globalIndex = startIndex + index + 1;

                    return (
                      <TableRow
                        key={rental.id}
                        className="border-b border-border hover:bg-muted/50 transition-colors"
                      >
                        <TableCell className="px-6 py-4 font-medium text-foreground">
                          {globalIndex}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="min-w-0">
                            <div className="font-medium text-foreground truncate">
                              {rental.product.name}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {rental.product.capacity} KVA
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="min-w-0">
                            <div className="font-medium text-foreground truncate">
                              {rental.user.name}
                            </div>
                            <div className="text-xs text-muted-foreground font-mono">
                              {rental.user.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-4 text-foreground">
                          {formatDate(rental.startDate)}
                        </TableCell>
                        <TableCell className="px-6 py-4 text-foreground">
                          {formatDate(rental.endDate)}
                        </TableCell>
                        <TableCell className="px-6 py-4 text-foreground">
                          {calculateDuration(rental.startDate, rental.endDate)}
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <Badge className={statusBadge.color}>
                            {statusBadge.label}
                          </Badge>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Buka menu</span>
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/admin/rentals/${rental.id}`}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  Lihat Detail
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination - Form Submission */}
      {filteredRentals.length > 0 && totalPages > 1 && (
        <div className="flex items-center justify-between mt-6 px-2">
          <div className="text-sm text-muted-foreground">
            Menampilkan {startIndex + 1}-{Math.min(endIndex, filteredRentals.length)} dari {filteredRentals.length} data
          </div>

          <div className="flex items-center gap-2">
            <form method="GET" style={{ display: 'inline' }}>
              <input type="hidden" name="page" value={Math.max(1, currentPage - 1)} />
              <button
                type="submit"
                disabled={currentPage <= 1}
                className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
            </form>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <form key={page} method="GET" style={{ display: 'inline' }}>
                <input type="hidden" name="page" value={page} />
                <button
                  type="submit"
                  className={`px-3 py-2 text-sm border rounded-md min-w-[40px] ${
                    page === currentPage
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              </form>
            ))}

            <form method="GET" style={{ display: 'inline' }}>
              <input type="hidden" name="page" value={Math.min(totalPages, currentPage + 1)} />
              <button
                type="submit"
                disabled={currentPage >= totalPages}
                className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
