import { notFound } from "next/navigation";
import { getProductById } from "@/lib/data/product";
import { EditProductForm } from "./edit-product-form";

interface EditProductPageProps {
    searchParams: { id: string };
}

export const dynamic = 'force-dynamic';

export default async function EditProductPage({ 
    searchParams 
}: EditProductPageProps) {
    const id = searchParams?.id;
    
    if (!id) {
        notFound();
    }

    const product = await getProductById(id);

    if (!product) {
        notFound();
    }

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">Edit Produk</h1>
            <EditProductForm product={product} />
        </div>
    );
} 