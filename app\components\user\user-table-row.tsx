"use client";

import { memo, useState } from 'react';
import { useSession } from "next-auth/react";
import { User } from "@/lib/types/user";
import { Clock, Mail, Edit, Trash2, MoreVertical, Phone } from "lucide-react";
import { UserAvatar } from "./user-avatar";
import { RoleSelector } from "./role-selector";
import { Button } from "@/app/components/ui/button";
import { formatDate } from "@/lib/utils/format";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { toast } from "sonner";
import { EditUserModal } from "./edit-user-modal";
import { ConfirmDialog } from "@/app/components/ui/confirm-dialog";
import { cn } from "@/lib/utils/cn";

interface UserTableRowProps {
  user: User;
  onUpdate: () => void;
}

export const UserTableRow = memo(function UserTableRow({ user, onUpdate }: UserTableRowProps) {
  const { data: session } = useSession();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Logika untuk menentukan apakah user dapat dimodifikasi
  const currentUserId = session?.user?.id;
  const isCurrentUser = user.id === currentUserId;
  const isTargetAdmin = user.role === 'ADMIN' || user.role === 'admin';
  const canModify = !isCurrentUser && !isTargetAdmin;

  const handleEdit = () => {
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = () => {
    if (!canModify) {
      if (isCurrentUser) {
        toast.error('Tidak dapat menghapus akun diri sendiri');
      } else if (isTargetAdmin) {
        toast.error('Tidak dapat menghapus admin lain');
      }
      return;
    }
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menghapus pengguna');
      }

      toast.success('Pengguna berhasil dihapus');
      setIsDeleteDialogOpen(false);
      onUpdate();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error(error instanceof Error ? error.message : 'Gagal menghapus pengguna');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <tr className="group hover:bg-accent/50 transition-colors duration-200">
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center space-x-4">
            <UserAvatar
              imageUrl={user.image}
              name={user.name || ''}
              size="sm"
            />
            <div className="min-w-0 flex-1">
              <div className="text-sm font-semibold text-foreground truncate flex items-center gap-2">
                {user.name || 'Nama tidak tersedia'}
                {isCurrentUser && (
                  <span className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 px-1.5 py-0.5 rounded">
                    Anda
                  </span>
                )}
              </div>
              <div className="text-xs text-muted-foreground font-mono">
                ID: {user.id.slice(0, 8)}...
              </div>
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="space-y-1">
            <div className="flex items-center text-sm text-foreground">
              <Mail className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
              <span className="truncate">{user.email}</span>
            </div>
            {user.phone && (
              <div className="flex items-center text-xs text-muted-foreground">
                <Phone className="h-3 w-3 mr-2 text-muted-foreground flex-shrink-0" />
                <span>{user.phone}</span>
              </div>
            )}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <RoleSelector
            userId={user.id}
            currentRole={user.role}
            onUpdate={onUpdate}
          />
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
            <span className="text-xs">
              {user.createdAt ? formatDate(user.createdAt) : '-'}
            </span>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-right">
          <div className="flex items-center justify-end space-x-2">
            {/* Quick Edit Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              disabled={isCurrentUser}
              className={cn(
                "h-11 w-11 p-0 text-muted-foreground hover:text-blue-600 hover:bg-blue-50 dark:hover:text-blue-400 dark:hover:bg-blue-900/20",
                "transition-all duration-200 opacity-0 group-hover:opacity-100",
                isCurrentUser && "opacity-50 cursor-not-allowed hover:bg-transparent hover:text-muted-foreground"
              )}
              title={isCurrentUser ? "Tidak dapat mengedit diri sendiri" : "Edit pengguna"}
            >
              <Edit className="h-4 w-4" />
            </Button>

            {/* More Actions Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-11 w-11 p-0 text-muted-foreground hover:text-foreground hover:bg-accent",
                    "transition-all duration-200"
                  )}
                  title="Opsi lainnya"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-48 bg-popover border-border shadow-lg"
              >
                <DropdownMenuItem
                  onClick={handleEdit}
                  disabled={isCurrentUser}
                  className={cn(
                    "flex items-center space-x-2 text-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer",
                    isCurrentUser && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <Edit className="h-4 w-4" />
                  <span>Edit Pengguna</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-border" />
                <DropdownMenuItem
                  onClick={handleDeleteClick}
                  disabled={isDeleting || !canModify}
                  className={cn(
                    "flex items-center space-x-2 text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 cursor-pointer",
                    (isDeleting || !canModify) && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <Trash2 className="h-4 w-4" />
                  <span>
                    {isDeleting ? 'Menghapus...' :
                     !canModify ? (isCurrentUser ? 'Tidak dapat hapus diri sendiri' : 'Tidak dapat hapus admin') :
                     'Hapus Pengguna'}
                  </span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </td>
      </tr>

      <EditUserModal
        user={user}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onUpdate={onUpdate}
      />

      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Hapus Pengguna"
        description="Apakah Anda yakin ingin menghapus pengguna ini? Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data terkait pengguna."
        confirmText="Hapus Pengguna"
        cancelText="Batal"
        variant="destructive"
        isLoading={isDeleting}
        onConfirm={handleDeleteConfirm}
        userInfo={{
          name: user.name || 'Nama tidak tersedia',
          email: user.email
        }}
      />
    </>
  );
});
