import React from "react";

interface AdminHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  action?: React.ReactNode;
}

export function AdminHeader({ title, description, actions, action }: AdminHeaderProps) {
  const actionContent = actions || action;
  
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between py-6 gap-4">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && <p className="text-muted-foreground mt-1">{description}</p>}
      </div>
      {actionContent && <div>{actionContent}</div>}
    </div>
  );
} 
