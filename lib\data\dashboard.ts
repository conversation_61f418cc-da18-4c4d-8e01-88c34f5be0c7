import { DashboardStats } from "../types/dashboard";

export async function getStats(): Promise<DashboardStats> {
  try {
    // Mendapatkan session cookie jika di server
    const fetchOptions: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Selalu sertakan credentials
      cache: 'no-store'
    };
    
    // Jika di server, gunakan URL absolut
    const isServer = typeof window === 'undefined';
    const baseUrl = isServer 
      ? process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
      : '';
    
    // Catatan: Kita menggunakan credentials: 'include' dan sudah cukup untuk Next.js
    // Next.js akan secara otomatis menangani cookies saat memanggil API route internal
    
    console.log(`Fetching dashboard stats from: ${baseUrl}/api/dashboard/stats`);
    const response = await fetch(`${baseUrl}/api/dashboard/stats`, fetchOptions);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch stats: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    
    // Return dummy data if the API call fails
    return {
      totalRevenue: 25000000,
      revenueGrowth: 12.5,
      totalRentals: 48,
      rentalGrowth: 8.3,
      totalProducts: 24,
      productGrowth: 4.2,
      occupancyRate: 78,
      occupancyGrowth: 5.6,
      recentRentals: [
        {
          id: "1",
          productName: "Genset 10kVA",
          customerName: "PT Maju Bersama",
          amount: 2500000,
          status: "COMPLETED",
          date: new Date(2023, 10, 25)
        },
        {
          id: "2",
          productName: "Genset 15kVA",
          customerName: "CV Sentosa",
          amount: 3000000,
          status: "ACTIVE",
          date: new Date(2023, 10, 20)
        },
        {
          id: "3",
          productName: "Genset 5kVA",
          customerName: "Toko Makmur",
          amount: 1500000,
          status: "PENDING",
          date: new Date(2023, 10, 18)
        },
        {
          id: "4",
          productName: "Genset 20kVA",
          customerName: "Hotel Bahagia",
          amount: 4000000,
          status: "COMPLETED",
          date: new Date(2023, 10, 15)
        },
        {
          id: "5",
          productName: "Genset 8kVA",
          customerName: "Rumah Sakit Sehat",
          amount: 2000000,
          status: "ACTIVE",
          date: new Date(2023, 10, 10)
        }
      ],
      monthlyRevenue: [
        { month: "Jan", revenue: 15000000 },
        { month: "Feb", revenue: 18000000 },
        { month: "Mar", revenue: 21000000 },
        { month: "Apr", revenue: 19000000 },
        { month: "Mei", revenue: 22000000 },
        { month: "Jun", revenue: 25000000 },
        { month: "Jul", revenue: 23000000 },
        { month: "Agu", revenue: 26000000 },
        { month: "Sep", revenue: 24000000 },
        { month: "Okt", revenue: 28000000 },
        { month: "Nov", revenue: 30000000 },
        { month: "Des", revenue: 35000000 }
      ],
      monthlyRentals: [
        { month: "Jan", count: 10 },
        { month: "Feb", count: 12 },
        { month: "Mar", count: 15 },
        { month: "Apr", count: 13 },
        { month: "Mei", count: 16 },
        { month: "Jun", count: 18 },
        { month: "Jul", count: 17 },
        { month: "Agu", count: 20 },
        { month: "Sep", count: 19 },
        { month: "Okt", count: 22 },
        { month: "Nov", count: 25 },
        { month: "Des", count: 30 }
      ],
      topProducts: [
        { id: "1", name: "Genset 100 KVA", capacity: 100, rentalCount: 28 },
        { id: "2", name: "Genset 50 KVA", capacity: 50, rentalCount: 22 },
        { id: "3", name: "Genset 25 KVA", capacity: 25, rentalCount: 18 },
        { id: "4", name: "Genset 10 KVA", capacity: 10, rentalCount: 15 }
      ]
    };
  }
}
