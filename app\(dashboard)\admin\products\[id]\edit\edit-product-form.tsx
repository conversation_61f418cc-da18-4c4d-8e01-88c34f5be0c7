"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { EditProductModal } from "@/app/components/product/edit-product-modal";
import { Product } from "@/lib/types/product";

export function EditProductForm({ product }: { product: Product }) {
  const [isOpen, setIsOpen] = useState(true);
  const router = useRouter();

  return (
    <EditProductModal
      product={product}
      isOpen={isOpen}
      onClose={() => {
        setIsOpen(false);
        router.back();
      }}
      onUpdate={() => {
        router.refresh();
        router.back();
      }}
    />
  );
} 