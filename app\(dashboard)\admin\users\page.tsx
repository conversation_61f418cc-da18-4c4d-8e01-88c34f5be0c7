import { Metadata } from "next";
import { AdminHeader } from "@/app/components/admin/admin-header";
import { Suspense } from "react";
import { LoadingState } from "@/app/components/shared/loading-state";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { UserTableServer } from "@/app/components/user/user-table-server";

export const metadata: Metadata = {
  title: "Manajemen Pengguna",
  description: "Kelola pengguna rental genset",
};

interface UserPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
  }>;
}

export default async function UserPage({ searchParams }: UserPageProps) {
  const session = await auth();

  if (!session?.user || session.user.role !== 'ADMIN') {
    redirect('/login');
  }

  const params = await searchParams;
  const currentPage = parseInt(params.page || '1', 10);
  const searchQuery = params.search || '';

  return (
    <>
      <AdminHeader
        title="Manajemen Pengguna"
        description="Kelola data pengguna"
      />

      <div className="max-w-7xl mx-auto mt-5 px-4 sm:px-6 lg:px-8">
        <Suspense fallback={<LoadingState />}>
          <UserTableServer currentPage={currentPage} searchQuery={searchQuery} />
        </Suspense>
      </div>
    </>
  );
}
